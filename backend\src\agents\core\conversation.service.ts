import { DataSource } from 'typeorm';
import {
	AgentRuntimeCallbacks,
	ConversationNotification,
	GenerateMessageInput,
	ProcessUserMessageInput,
	buildConversationAgent,
	buildProductContextMessage,
	buildCustomerContextMessage,
	ensureLLMProviderConfigured,
	toBigInt,
} from './conversation-agent';

import { LlmApi, type Message as AgentMessage } from '../../../../ai-agent/dist';
import { Conversation } from '../../conversations/conversation.entity';
import { Store } from '../../stores/store.entity';
import { Message } from '../../conversations/message.entity';
import { Product } from '../../products/product.entity';
import { ToolCall } from '../../conversations/tool-call.entity';

// ---- Internal helpers (keep file-local; no exports) ----
async function getConversationByUuid(db: DataSource, conversationUuid: string) {
	const conversationRepository = db.getRepository(Conversation);
	return conversationRepository.findOne({
		where: { uuid: conversationUuid, isDeleted: false },
		select: ['id', 'storeId'],
	});
}

async function getStoreCurrency(db: DataSource, storeId: bigint) {
	const storeRepository = db.getRepository(Store);
	const store = await storeRepository.findOne({
		where: { id: storeId.toString(), isDeleted: false },
		select: ['currency'],
	});
	return store?.currency || 'USD';
}

async function getStorePreferredLanguage(db: DataSource, storeId: bigint) {
	const storeRepository = db.getRepository(Store);
	const store = await storeRepository.findOne({
		where: { id: storeId.toString(), isDeleted: false },
		select: ['preferredLanguage'],
	});
	return store?.preferredLanguage || 'en';
}



// Helper function to extract metrics from LlmResponseResult
function extractMetricsFromResponse(response: any) {
	return {
		totalCost: response.totalCost || 0,
		totalInputTokens: response.totalInputTokens || 0,
		totalOutputTokens: response.totalOutputTokens || 0,
		totalExecutionTime: response.totalExecutionTime || 0,
	};
}



// Helper function to update conversation totals
async function updateConversationTotals(db: DataSource, conversationId: bigint) {
	try {
		const conversationRepository = db.getRepository(Conversation);

		// Update conversation timestamp
		// Note: Conversation entity doesn't have cost/token tracking fields
		// This would need to be added to the entity if tracking is required
		await conversationRepository.update(
			{ id: conversationId.toString() },
			{
				updatedAt: new Date(),
			}
		);
	} catch (error) {
		console.warn('Failed to update conversation totals:', error);
	}
}

// Helper function to persist agent response to database
async function persistAgentResponse(params: {
	db: DataSource;
	agentId: string;
	ownerUserId: string | number | bigint;
	conversationId: bigint;
	response: string;
	toolCalls?: Array<{
		id: string;
		function: {
			name: string;
			arguments: string;
		};
	}>;
	toolOutputs?: Array<{
		role: string;
		content: string;
		tool_call_id: string;
	}>;
	executionDetails?: Array<{
		toolCallId: string;
		toolName: string;
		executionTime: number;
		success: boolean;
		errorMessage?: string;
		startTime: number;
		endTime: number;
		cost?: number;
		inputTokens?: number;
		outputTokens?: number;
	}>;
	llmCosts?: {
		totalCost: number;
		totalInputTokens: number;
		totalOutputTokens: number;
		totalExecutionTime?: number;
	};
}): Promise<{ content: string }> {
	const { db, ownerUserId, conversationId, response, toolCalls, toolOutputs, executionDetails, llmCosts } = params;

	const messageRepository = db.getRepository(Message);
	const toolCallRepository = db.getRepository(ToolCall);

	// Use timestamp-based ordering for sequence
	const nextSequence = Date.now();

	if (toolCalls && toolCalls.length > 0) {
		for (let i = 0; i < toolCalls.length; i++) {
			const toolCall = toolCalls[i];
			const toolOutput = toolOutputs?.find(to => to.tool_call_id === toolCall.id);
			const executionDetail = executionDetails?.find(ed => ed.toolCallId === toolCall.id);
			
			// Parse tool arguments
			let toolInput: any = {};
			try {
				toolInput = JSON.parse(toolCall.function.arguments);
			} catch (e) {
				toolInput = { raw: toolCall.function.arguments };
			}

			// Handle tool output - it might not be valid JSON
			let toolOutputData: any = undefined;
			if (toolOutput?.content) {
				try {
					// Try to parse as JSON first
					toolOutputData = JSON.parse(toolOutput.content);
				} catch (e) {
					// If it's not JSON, store as a string
					toolOutputData = { rawContent: toolOutput.content };
				}
			}

			// Create tool call record with the same sequence number
			try {
				await toolCallRepository.save({
					toolName: toolCall.function.name,
					toolInput,
					conversationId: conversationId.toString(),
					createdBy: toBigInt(ownerUserId).toString(),
					toolOutput: toolOutputData,
					executionTime: executionDetail?.executionTime,
					success: executionDetail?.success ?? true,
					errorMessage: executionDetail?.errorMessage,
					cost: executionDetail?.cost,
					inputTokens: executionDetail?.inputTokens,
					outputTokens: executionDetail?.outputTokens,
					sequence: nextSequence + i, // Increment sequence for each tool call
				});
			} catch (toolCallError) {
				// Continue with other tool calls even if one fails
				console.warn('Failed to create tool call record:', toolCallError);
			}
		}
	}

	try {
		// Create the agent message with sequence number after all tool calls
		const messageSequence = toolCalls && toolCalls.length > 0 ? nextSequence + toolCalls.length : nextSequence;
		const created = await messageRepository.save({
			content: response,
			role: 'assistant', // Set role for agent messages
			createdBy: toBigInt(ownerUserId).toString(),
			conversationId: conversationId.toString(),
			userId: toBigInt(ownerUserId).toString(), // Set userId as required field
			sequence: messageSequence,
			// Cost tracking fields
			cost: llmCosts?.totalCost,
			executionTime: llmCosts?.totalExecutionTime,
			inputTokens: llmCosts?.totalInputTokens,
			outputTokens: llmCosts?.totalOutputTokens,
		});

		// Update conversation totals after creating message and tool calls
		await updateConversationTotals(db, conversationId);

		return created;
	} catch (error) {
		console.error('Failed to persist agent response:', error);

		// Try to create with minimal data as fallback
		try {
			const messageSequence = toolCalls && toolCalls.length > 0 ? nextSequence + toolCalls.length : nextSequence;
			const fallbackMessage = await messageRepository.save({
				content: response || 'I apologize, but I encountered an error while processing your request.',
				role: 'assistant', // Set role for agent messages
				createdBy: toBigInt(ownerUserId).toString(),
				conversationId: conversationId.toString(),
				userId: toBigInt(ownerUserId).toString(), // Set userId as required field
				sequence: messageSequence, // Use the sequence number after tool calls
				// Cost tracking fields
				cost: llmCosts?.totalCost,
				executionTime: llmCosts?.totalExecutionTime,
				inputTokens: llmCosts?.totalInputTokens,
				outputTokens: llmCosts?.totalOutputTokens,
			});

			// Update conversation totals even for fallback message
			await updateConversationTotals(db, conversationId);

			return fallbackMessage;
		} catch (fallbackError) {
			console.error('Fallback message creation also failed:', fallbackError);
			throw new Error(`Failed to save agent response: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`);
		}
	}
}

// Helper function to safely update notification status
async function updateNotificationSafely(
	db: DataSource,
	conversationUuid: string,
	notification: ConversationNotification,
	callbacks?: AgentRuntimeCallbacks
): Promise<void> {
	try {
		const conversationRepository = db.getRepository(Conversation);
		// Update the database (notificationStatus not available in current entity)
		await conversationRepository.update(
			{ uuid: conversationUuid },
			{ updatedAt: new Date() }
		);

		// Call the callback if provided
		await callbacks?.setNotification?.(conversationUuid, notification);
	} catch (error) {
		console.warn(`Failed to update notification status to ${notification}:`, error);
	}
}

// Helper function to get conversation and store information together
async function getConversationAndStoreInfo(db: DataSource, conversationUuid: string) {
	const conversation = await getConversationByUuid(db, conversationUuid);
	if (!conversation) {
		throw new Error(`Conversation with UUID ${conversationUuid} not found`);
	}

	const storeCurrency = await getStoreCurrency(db, BigInt(conversation.storeId));
	const storePreferredLanguage = await getStorePreferredLanguage(db, BigInt(conversation.storeId));

	return {
		conversation,
		storeCurrency,
		storePreferredLanguage
	};
}

// Helper function to get conversation history
async function getConversationHistory(db: DataSource, conversationId: bigint): Promise<AgentMessage[]> {
	const messageRepository = db.getRepository(Message);
	const messageHistory = await messageRepository.find({
		where: { conversationId: conversationId.toString(), isDeleted: false },
		order: { createdAt: 'ASC' },
		select: ['content', 'role', 'userId'],
	});

	// Map messages to agent format (agentId and customerId not available in current entity)
	return messageHistory.map(msg => ({
		role: msg.role as 'user' | 'assistant',
		content: msg.content
	}));
}

// Helper function to get products for a store
async function getStoreProducts(db: DataSource, storeId: bigint): Promise<Array<{ id: bigint; name: string; description: string | null; price: number; sku: string | null }>> {
	const productRepository = db.getRepository(Product);
	const products = await productRepository.find({
		where: {
			storeId: storeId.toString(),
			isDeleted: false
		},
		select: ['id', 'name', 'description', 'price', 'sku'],
		order: { createdAt: 'DESC' },
		// Limit to prevent overwhelming the context
		take: 50
	});

	return products.map(product => ({
		id: BigInt(product.id),
		name: product.name,
		description: product.description,
		price: Number(product.price),
		sku: product.sku
	}));
}





export async function generateAgentMessage(
	db: DataSource,
	params: ProcessUserMessageInput | GenerateMessageInput,
	callbacks?: AgentRuntimeCallbacks
): Promise<string> {
	console.log(`🔧 generateAgentMessage called with:`, {
		dbType: typeof db,
		dbExists: !!db,
		dbInitialized: db?.isInitialized,
		params,
		timestamp: new Date().toISOString()
	});

	await ensureLLMProviderConfigured();
	const { conversationUuid, ownerUserId, agentId } = params;
	// userMessage is optional - if not provided, we'll generate a continuation message
	const userMessage = 'userMessage' in params ? params.userMessage : undefined;

	const startTime = Date.now();

	try {
		// Validate database connection is still valid
		if (!db || !db.isInitialized) {
			console.error(`❌ Database connection validation failed in generateAgentMessage:`, {
				dbExists: !!db,
				dbInitialized: db?.isInitialized,
				dbType: typeof db
			});
			throw new Error('Database connection is not properly initialized in generateAgentMessage');
		}

		console.log(`✅ Database connection validated in generateAgentMessage`);

		// Set initial thinking notification
		await updateNotificationSafely(db, conversationUuid, ConversationNotification.AgentIsThinking, callbacks);

		// Get conversation and store information
		const { conversation, storeCurrency, storePreferredLanguage } = await getConversationAndStoreInfo(db, conversationUuid);

		// Get products for the store
		const products = await getStoreProducts(db, BigInt(conversation.storeId));

		// Get customer information if conversation has a customer
		// Note: Customer functionality not yet implemented
		const customer = null;

		// Build conversation agent with basic context (no products/customer in system prompt)
		console.log(`🔧 Building conversation agent with database connection:`, {
			dbType: typeof db,
			dbInitialized: db?.isInitialized,
			conversationUuid,
			storeCurrency,
			storePreferredLanguage
		});
		const conversationAgent = buildConversationAgent(db, conversationUuid, storeCurrency, storePreferredLanguage);

		// Get conversation history
		const history = await getConversationHistory(db, BigInt(conversation.id));

		// Build context messages
		const productContextMessage = buildProductContextMessage(products, storeCurrency);
		const customerContextMessage = buildCustomerContextMessage(customer);

		// Combine context messages with conversation history
		const allMessages: AgentMessage[] = [];
		
		// Add context messages (they now always return a message)
		if (productContextMessage) allMessages.push(productContextMessage);
		if (customerContextMessage) allMessages.push(customerContextMessage);
		
		// Add conversation history
		allMessages.push(...history);
		
		// Add the current user message or continuation prompt
		if (userMessage) {
			allMessages.push({ role: 'user', content: userMessage });
		} else {
			// For generateMessage functionality - continue the conversation
			allMessages.push({ role: 'user', content: 'Continue the conversation.' });
		}

		// Update notification to show response generation
		await updateNotificationSafely(db, conversationUuid, ConversationNotification.AgentIsGeneratingResponse, callbacks);
		
		// Generate response using the conversation agent
		const response = await LlmApi.generateLlmResponse(conversationAgent, allMessages);
		
		// const sanitizedResponse = sanitizeAssistantReply(response.content);
		const sanitizedResponse = response.content;

		const executionTime = Date.now() - startTime;

		// Extract metrics from the response
		const metrics = extractMetricsFromResponse(response);

		// Persist the final response with tool call information
		const created = await persistAgentResponse({
			db,
			agentId,
			ownerUserId,
			conversationId: BigInt(conversation.id),
			response: sanitizedResponse,
			toolCalls: response.calls,
			toolOutputs: response.outputs,
			executionDetails: response.executionDetails,
			llmCosts: {
				...metrics,
				totalExecutionTime: executionTime,
			},
		});

		// Clear notification status
		await updateNotificationSafely(db, conversationUuid, ConversationNotification.None, callbacks);

		return created.content;
	} catch (error) {
		// Ensure notification is cleared on any error
		await updateNotificationSafely(db, conversationUuid, ConversationNotification.None, callbacks);
		throw error;
	}
}

// Backward compatibility wrapper for generateMessage functionality
export async function generateMessage(
	db: DataSource,
	input: GenerateMessageInput,
	callbacks?: AgentRuntimeCallbacks
): Promise<string> {
	// Call the unified generateAgentMessage function without a user message
	return generateAgentMessage(db, input, callbacks);
}

// Backward compatibility wrapper for processUserMessage functionality
export async function processUserMessage(
	db: DataSource,
	params: ProcessUserMessageInput,
	callbacks?: AgentRuntimeCallbacks
): Promise<string> {
	// Call the unified generateAgentMessage function with a user message
	return generateAgentMessage(db, params, callbacks);
}


